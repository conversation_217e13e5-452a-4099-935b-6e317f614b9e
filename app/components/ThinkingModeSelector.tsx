'use client'
import React, { useCallback } from 'react';
import { But<PERSON>, Dropdown } from 'antd';
import { CheckOutlined } from '@ant-design/icons';
import ThinkingIcon from '@/app/images/thinking.svg';
import { LLMModel } from '@/types/llm';

interface ThinkingModeSelectorProps {
  currentModel: LLMModel;
  thinkingMode: 'disabled' | 'light' | 'medium' | 'deep';
  setThinkingMode: (value: 'disabled' | 'light' | 'medium' | 'deep') => void;
}

const ThinkingModeSelector: React.FC<ThinkingModeSelectorProps> = ({
  currentModel,
  thinkingMode,
  setThinkingMode
}) => {
  // 处理思考模式选择
  const handleThinkingModeChange = useCallback((value: 'disabled' | 'light' | 'medium' | 'deep') => {
    setThinkingMode(value);
  }, [setThinkingMode]);

  // 检查是否支持 Claude 模型的 Thinking 模式
  const isClaudeThinkingSupported = ['claude-opus-4', 'claude-sonnet-4', 'claude-3-7-sonnet'].some(modelId =>
    currentModel.id.includes(modelId)
  ) && currentModel.provider.apiStyle === 'claude';

  // 如果不支持 Thinking 模式，不渲染组件
  if (!isClaudeThinkingSupported) {
    return null;
  }

  // 思考模式选项配置
  const thinkingOptions = [
    { key: 'disabled', label: '关闭', value: 'disabled' },
    { key: 'light', label: '浮想', value: 'light' },
    { key: 'medium', label: '斟酌', value: 'medium' },
    { key: 'deep', label: '沉思', value: 'deep' },
  ];

  // 获取当前选中的思考模式标签
  const getCurrentThinkingLabel = () => {
    const option = thinkingOptions.find(opt => opt.value === thinkingMode);
    if (option?.key === 'disabled') {
      return '深度思考';
    } else {
      return option ? option.label : '深度思考';
    }
  };

  // 思考模式下拉菜单项
  const thinkingMenuItems = thinkingOptions.map(option => ({
    key: option.key,
    label: (
      <div className="flex items-center justify-between min-w-20">
        <span>{option.label}</span>
        {thinkingMode === option.value && (
          <CheckOutlined className="text-blue-500 ml-2" style={{ fontSize: '12px' }} />
        )}
      </div>
    ),
    onClick: () => handleThinkingModeChange(option.value as 'disabled' | 'light' | 'medium' | 'deep'),
    className: thinkingMode === option.value ? 'bg-blue-50' : '',
  }));

  return (
    <Dropdown
      menu={{ items: thinkingMenuItems }}
      trigger={['click']}
      placement="topLeft"
    >
      <Button
        type="text"
        size='small'
        className={thinkingMode !== 'disabled' ? 'text-blue-600' : ''}
        style={{
          color: thinkingMode !== 'disabled' ? '#1677ff' : 'gray'
        }}
      >
        <ThinkingIcon
          style={{
            width: '13px',
            height: '13px',
            color: thinkingMode !== 'disabled' ? '#1677ff' : 'gray'
          }}
        />
        <span className={`text-xs -ml-1 hidden sm:inline ${thinkingMode !== 'disabled' ? 'text-blue-600' : 'text-gray-500'
          }`}>
          {getCurrentThinkingLabel()}
        </span>
      </Button>
    </Dropdown>
  );
};

export default ThinkingModeSelector;
